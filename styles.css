/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-color: #4A90E2;
    --primary-dark: #357ABD;
    --secondary-color: #6C7B7F;
    --accent-color: #FF6B6B;
    --background-primary: #FFFFFF;
    --background-secondary: #F8FAFC;
    --background-dark: #1E293B;
    --text-primary: #1E293B;
    --text-secondary: #64748B;
    --text-light: #94A3B8;
    --border-color: #E2E8F0;
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 10px 25px rgba(0, 0, 0, 0.15);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing (8px grid system) */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    overflow-x: hidden;
}

/* Container and Grid System */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

@media (min-width: 640px) {
    .container {
        padding: 0 var(--space-6);
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 var(--space-8);
    }
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: var(--transition-normal);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4) var(--space-6);
    max-width: 1200px;
    margin: 0 auto;
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    text-decoration: none;
    color: var(--text-primary);
}

.logo svg {
    border-radius: var(--radius-full);
    transition: var(--transition-normal);
}

.logo:hover svg {
    transform: scale(1.05);
    box-shadow: var(--shadow-medium);
}

.logo-text {
    font-size: var(--font-size-lg);
    font-weight: 700;
    letter-spacing: 0.5px;
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--space-8);
}

.nav-links {
    display: flex;
    list-style: none;
    gap: var(--space-6);
    margin: 0;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
}

.nav-links a:hover {
    color: var(--primary-color);
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: var(--transition-fast);
}

.nav-links a:hover::after {
    width: 100%;
}

.login-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.login-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
    padding: var(--space-2);
}

.mobile-menu-toggle span {
    width: 24px;
    height: 2px;
    background: var(--text-primary);
    transition: var(--transition-fast);
}

/* Main Content */
.main-content {
    margin-top: 80px;
}

/* Hero Section */
.hero-section {
    min-height: 90vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--background-secondary) 0%, #E0F2FE 100%);
    position: relative;
    overflow: hidden;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-12);
    align-items: center;
    padding: var(--space-16) 0;
}

.hero-text h1 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--space-6);
    color: var(--text-primary);
}

.hero-text p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--space-8);
    max-width: 500px;
}

.cta-buttons {
    display: flex;
    gap: var(--space-4);
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: var(--transition-normal);
    border: 2px solid transparent;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

/* Hero Image */
.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.image-container {
    position: relative;
    width: 400px;
    height: 400px;
}

.globe-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.globe {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 50%, #2563EB 100%);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-heavy);
    animation: rotate 20s linear infinite;
}

.globe::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3) 0%, transparent 50%),
        linear-gradient(45deg, transparent 40%, rgba(255,255,255,0.1) 50%, transparent 60%);
    border-radius: 50%;
}

.airplane {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2rem;
    animation: fly 8s ease-in-out infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes fly {
    0%, 100% { transform: translate(-50%, -50%) rotate(0deg) translateX(120px) rotate(0deg); }
    25% { transform: translate(-50%, -50%) rotate(90deg) translateX(120px) rotate(-90deg); }
    50% { transform: translate(-50%, -50%) rotate(180deg) translateX(120px) rotate(-180deg); }
    75% { transform: translate(-50%, -50%) rotate(270deg) translateX(120px) rotate(-270deg); }
}

/* Services Section */
.services-section {
    padding: var(--space-20) 0;
    background: var(--background-primary);
}

.services-section h2 {
    text-align: center;
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--space-16);
    color: var(--text-primary);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-8);
}

.service-card {
    background: var(--background-primary);
    padding: var(--space-8);
    border-radius: var(--radius-xl);
    text-align: center;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    transition: var(--transition-normal);
}

.service-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-color);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: var(--space-4);
}

.service-card h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--space-3);
    color: var(--text-primary);
}

.service-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Mobile Navigation */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        left: 0;
        right: 0;
        background: var(--background-primary);
        flex-direction: column;
        padding: var(--space-6);
        box-shadow: var(--shadow-heavy);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition-normal);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-links {
        flex-direction: column;
        gap: var(--space-4);
        margin-bottom: var(--space-6);
    }

    .nav-links a {
        font-size: var(--font-size-lg);
        padding: var(--space-3) 0;
        border-bottom: 1px solid var(--border-color);
    }

    .login-btn {
        width: 100%;
        padding: var(--space-4);
        font-size: var(--font-size-lg);
    }

    .mobile-menu-toggle {
        display: flex;
    }
    
    .hero-text h1 {
        font-size: var(--font-size-2xl);
    }
    
    .hero-text p {
        font-size: var(--font-size-base);
    }
    
    .cta-buttons {
        flex-direction: column;
    }
    
    .btn-primary, .btn-secondary {
        width: 100%;
        text-align: center;
    }
    
    .image-container {
        width: 300px;
        height: 300px;
    }
    
    .globe {
        width: 250px;
        height: 250px;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
}

@media (min-width: 769px) {
    .hero-content {
        grid-template-columns: 1fr 1fr;
    }
    
    .hero-text h1 {
        font-size: var(--font-size-4xl);
    }
}

@media (min-width: 1024px) {
    .hero-text h1 {
        font-size: 3.5rem;
    }

    .services-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-heavy);
    width: 90%;
    max-width: 400px;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(20px);
    transition: var(--transition-normal);
}

.modal-overlay.active .modal-container {
    transform: scale(1) translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.close-btn:hover {
    background: var(--background-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--space-6);
}

/* Login Form Styles */
.login-form {
    margin-bottom: var(--space-6);
}

.form-group {
    position: relative;
    margin-bottom: var(--space-4);
}

.form-group input {
    width: 100%;
    padding: var(--space-4);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    transition: var(--transition-fast);
    background: var(--background-primary);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.form-group input::placeholder {
    color: var(--text-light);
}

.password-toggle {
    position: absolute;
    right: var(--space-3);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    transition: var(--transition-fast);
}

.password-toggle:hover {
    color: var(--text-primary);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
    font-size: var(--font-size-sm);
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    cursor: pointer;
    color: var(--text-secondary);
}

.checkbox-container input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
}

.forgot-password:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.login-submit-btn {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--space-4);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
}

.login-submit-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

.divider {
    text-align: center;
    margin: var(--space-6) 0;
    position: relative;
    color: var(--text-light);
    font-size: var(--font-size-sm);
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
    z-index: 1;
}

.divider span {
    background: var(--background-primary);
    padding: 0 var(--space-4);
    position: relative;
    z-index: 2;
}

.social-login {
    display: flex;
    gap: var(--space-3);
    margin-bottom: var(--space-6);
}

.social-btn {
    flex: 1;
    padding: var(--space-3);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--background-primary);
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-weight: 500;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
}

.social-btn:hover {
    border-color: var(--primary-color);
    background: var(--background-secondary);
}

.phone-btn:hover {
    border-color: #10B981;
    color: #10B981;
}

.google-btn:hover {
    border-color: #EA4335;
    color: #EA4335;
}

.facebook-btn:hover {
    border-color: #1877F2;
    color: #1877F2;
}

.signup-link {
    text-align: center;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.signup-link a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.signup-link a:hover {
    text-decoration: underline;
}

/* Footer Styles */
.footer {
    background: var(--background-dark);
    color: var(--text-light);
    padding: var(--space-16) 0 var(--space-8);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-8);
    margin-bottom: var(--space-8);
}

.footer-section h4 {
    color: white;
    font-weight: 600;
    margin-bottom: var(--space-4);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--space-2);
}

.footer-section ul li a {
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.footer-section .logo {
    margin-bottom: var(--space-4);
}

.footer-section .logo-text {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: var(--space-8);
    border-top: 1px solid #374151;
    color: var(--text-light);
}

/* Accessibility and Focus States */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus,
input:focus,
a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.loading {
    animation: pulse 2s infinite;
}

/* Print Styles */
@media print {
    .header,
    .modal-overlay,
    .footer {
        display: none;
    }

    .main-content {
        margin-top: 0;
    }
}
